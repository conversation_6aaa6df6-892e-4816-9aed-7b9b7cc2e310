/**
 * useDynamicKpis Simple Tests
 * 
 * Simplified tests to validate TDD approach without complex setup.
 * Focus on core functionality and business logic.
 */

import { describe, it, expect } from 'vitest';
import { kpiHelpers } from '../useDynamicKpis';
import { generateMockKpi } from '../../testing/testUtils';

describe('useDynamicKpis - Core Logic', () => {
  describe('kpiHelpers', () => {
    describe('sortKpis', () => {
      it('should sort KPIs by priority first, then by display order', () => {
        const kpis = [
          generateMockKpi({ id: 'kpi1', is_priority: false, display_order: 2 }),
          generateMockKpi({ id: 'kpi2', is_priority: true, display_order: 3 }),
          generateMockKpi({ id: 'kpi3', is_priority: true, display_order: 1 }),
          generateMockKpi({ id: 'kpi4', is_priority: false, display_order: 1 }),
        ];

        const sorted = kpiHelpers.sortKpis(kpis);

        // Priority KPIs should come first
        expect(sorted[0].id).toBe('kpi3'); // Priority + order 1
        expect(sorted[1].id).toBe('kpi2'); // Priority + order 3
        
        // Non-priority KPIs should come after, sorted by order
        expect(sorted[2].id).toBe('kpi4'); // Non-priority + order 1
        expect(sorted[3].id).toBe('kpi1'); // Non-priority + order 2
      });

      it('should handle empty array', () => {
        const result = kpiHelpers.sortKpis([]);
        expect(result).toEqual([]);
      });

      it('should not mutate original array', () => {
        const original = [
          generateMockKpi({ id: 'kpi1', is_priority: false, display_order: 2 }),
          generateMockKpi({ id: 'kpi2', is_priority: true, display_order: 1 }),
        ];
        const originalCopy = [...original];

        kpiHelpers.sortKpis(original);

        expect(original).toEqual(originalCopy);
      });
    });

    describe('filterByCategory', () => {
      it('should filter KPIs by exact category match', () => {
        const kpis = [
          generateMockKpi({ id: 'kpi1', category: 'volume' }),
          generateMockKpi({ id: 'kpi2', category: 'performance' }),
          generateMockKpi({ id: 'kpi3', category: 'volume' }),
          generateMockKpi({ id: 'kpi4', category: 'compliance' }),
        ];

        const volumeKpis = kpiHelpers.filterByCategory(kpis, 'volume');
        
        expect(volumeKpis).toHaveLength(2);
        expect(volumeKpis[0].id).toBe('kpi1');
        expect(volumeKpis[1].id).toBe('kpi3');
        expect(volumeKpis.every(kpi => kpi.category === 'volume')).toBe(true);
      });

      it('should return empty array for non-existent category', () => {
        const kpis = [
          generateMockKpi({ category: 'volume' }),
          generateMockKpi({ category: 'performance' }),
        ];

        const result = kpiHelpers.filterByCategory(kpis, 'nonexistent');
        expect(result).toEqual([]);
      });

      it('should handle empty input array', () => {
        const result = kpiHelpers.filterByCategory([], 'volume');
        expect(result).toEqual([]);
      });
    });

    describe('getCategories', () => {
      it('should return unique categories sorted alphabetically', () => {
        const kpis = [
          generateMockKpi({ category: 'volume' }),
          generateMockKpi({ category: 'performance' }),
          generateMockKpi({ category: 'volume' }), // duplicate
          generateMockKpi({ category: 'compliance' }),
          generateMockKpi({ category: 'performance' }), // duplicate
        ];

        const categories = kpiHelpers.getCategories(kpis);
        
        expect(categories).toEqual(['compliance', 'performance', 'volume']);
        expect(categories).toHaveLength(3); // No duplicates
      });

      it('should handle empty array', () => {
        const result = kpiHelpers.getCategories([]);
        expect(result).toEqual([]);
      });

      it('should handle single category', () => {
        const kpis = [
          generateMockKpi({ category: 'volume' }),
          generateMockKpi({ category: 'volume' }),
        ];

        const result = kpiHelpers.getCategories(kpis);
        expect(result).toEqual(['volume']);
      });
    });

    describe('getPriorityKpis', () => {
      it('should return only KPIs marked as priority', () => {
        const kpis = [
          generateMockKpi({ id: 'kpi1', is_priority: true }),
          generateMockKpi({ id: 'kpi2', is_priority: false }),
          generateMockKpi({ id: 'kpi3', is_priority: true }),
          generateMockKpi({ id: 'kpi4', is_priority: false }),
        ];

        const priorityKpis = kpiHelpers.getPriorityKpis(kpis);
        
        expect(priorityKpis).toHaveLength(2);
        expect(priorityKpis[0].id).toBe('kpi1');
        expect(priorityKpis[1].id).toBe('kpi3');
        expect(priorityKpis.every(kpi => kpi.is_priority)).toBe(true);
      });

      it('should return empty array when no priority KPIs exist', () => {
        const kpis = [
          generateMockKpi({ is_priority: false }),
          generateMockKpi({ is_priority: false }),
        ];

        const result = kpiHelpers.getPriorityKpis(kpis);
        expect(result).toEqual([]);
      });

      it('should handle empty array', () => {
        const result = kpiHelpers.getPriorityKpis([]);
        expect(result).toEqual([]);
      });
    });
  });

  describe('Dynamic Query Building Logic', () => {
    it('should build query parameters correctly', () => {
      const filters = {
        timeframe: 'month',
        currency: 'usd',
        category: 'volume',
        priority_only: true,
        client_ids: ['L2M', 'CLIENT2'], // Array value
      };

      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, v.toString()));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });

      const queryString = queryParams.toString();
      
      expect(queryString).toContain('timeframe=month');
      expect(queryString).toContain('currency=usd');
      expect(queryString).toContain('category=volume');
      expect(queryString).toContain('priority_only=true');
      expect(queryString).toContain('client_ids=L2M');
      expect(queryString).toContain('client_ids=CLIENT2');
    });

    it('should handle undefined and null values', () => {
      const filters = {
        timeframe: 'week',
        currency: undefined,
        category: null,
        priority_only: false,
      };

      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const queryString = queryParams.toString();
      
      expect(queryString).toContain('timeframe=week');
      expect(queryString).toContain('priority_only=false');
      expect(queryString).not.toContain('currency');
      expect(queryString).not.toContain('category');
    });
  });

  describe('API Response Processing Logic', () => {
    it('should process successful API response correctly', () => {
      const mockApiResponse = {
        data: [
          generateMockKpi({ id: 'kpi1' }),
          generateMockKpi({ id: 'kpi2' }),
        ],
        success: true,
        timestamp: '2024-01-01T00:00:00Z',
        metadata: {
          total_count: 2,
          execution_time_ms: 50,
        },
      };

      // Simulate processing logic
      const processedData = {
        kpis: mockApiResponse.data,
        totalCount: mockApiResponse.metadata.total_count,
        lastUpdated: mockApiResponse.timestamp,
        error: null,
      };

      expect(processedData.kpis).toHaveLength(2);
      expect(processedData.totalCount).toBe(2);
      expect(processedData.lastUpdated).toBe('2024-01-01T00:00:00Z');
      expect(processedData.error).toBeNull();
    });

    it('should handle API error response correctly', () => {
      const mockErrorResponse = {
        data: [],
        success: false,
        error: 'Database connection failed',
        timestamp: '2024-01-01T00:00:00Z',
      };

      // Simulate error processing logic
      const processedData = {
        kpis: [],
        totalCount: 0,
        lastUpdated: null,
        error: mockErrorResponse.error,
      };

      expect(processedData.kpis).toEqual([]);
      expect(processedData.totalCount).toBe(0);
      expect(processedData.error).toBe('Database connection failed');
    });
  });
});

describe('Type Safety Validation', () => {
  it('should ensure KPI data structure matches expected interface', () => {
    const kpi = generateMockKpi({
      id: 'test_kpi',
      name: 'Test KPI',
      category: 'test',
      is_priority: true,
      display_order: 1,
      currentValue: 1000,
    });

    // Type assertions to ensure structure
    expect(typeof kpi.id).toBe('string');
    expect(typeof kpi.name).toBe('string');
    expect(typeof kpi.category).toBe('string');
    expect(typeof kpi.is_priority).toBe('boolean');
    expect(typeof kpi.display_order).toBe('number');
    expect(typeof kpi.currentValue).toBe('number');
    expect(Array.isArray(kpi.chartData)).toBe(true);
  });

  it('should validate filter structure', () => {
    const filters = {
      timeframe: 'week',
      currency: 'usd',
      priority_only: true,
    };

    expect(typeof filters.timeframe).toBe('string');
    expect(typeof filters.currency).toBe('string');
    expect(typeof filters.priority_only).toBe('boolean');
  });
});
