/**
 * useDynamicKpis Hook Tests
 *
 * Tests for the dynamic KPI hook that loads KPIs without hardcoding.
 * Following TDD approach - tests written first, then implementation.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { useDynamicKpis, kpiHelpers } from '../useDynamicKpis';
import {
  mockFetch,
  mockFetchError,
  cleanupMocks,
  generateMockKpi,
  generateMockFilters,
  mockApiResponse
} from '../../testing/testUtils';

describe('useDynamicKpis', () => {
  beforeEach(() => {
    cleanupMocks();
  });

  describe('Initial Loading', () => {
    it('should start with initial loading state', () => {
      const filters = generateMockFilters();
      const { result } = renderHook(() => useDynamicKpis({ filters }));

      expect(result.current.isInitialLoading).toBe(true);
      expect(result.current.isLoading).toBe(true);
      expect(result.current.kpis).toEqual([]);
      expect(result.current.error).toBeNull();
    });

    it('should fetch KPIs on mount', async () => {
      const mockKpis = [generateMockKpi({ id: 'kpi1' }), generateMockKpi({ id: 'kpi2' })];
      const mockResponse = mockApiResponse(mockKpis);
      mockFetch(mockResponse);

      const filters = generateMockFilters();
      const { result } = renderHook(() => useDynamicKpis({ filters }));

      await waitFor(() => {
        expect(result.current.isInitialLoading).toBe(false);
      });

      expect(result.current.kpis).toEqual(mockKpis);
      expect(result.current.totalCount).toBe(2);
      expect(result.current.error).toBeNull();
    });

    it('should handle API errors gracefully', async () => {
      mockFetchError('Server Error', 500);

      const filters = generateMockFilters();
      const { result } = renderHook(() => useDynamicKpis({ filters }), renderHookWithProviders(() => useDynamicKpis({ filters })));

      await waitFor(() => {
        expect(result.current.isInitialLoading).toBe(false);
      });

      expect(result.current.error).toContain('API Error: 500');
      expect(result.current.hasError).toBe(true);
      expect(result.current.kpis).toEqual([]);
    });
  });

  describe('Filter Changes', () => {
    it('should refetch KPIs when filters change', async () => {
      const initialKpis = [generateMockKpi({ id: 'kpi1' })];
      const updatedKpis = [generateMockKpi({ id: 'kpi2' })];
      
      mockFetch(mockApiResponse(initialKpis));

      const initialFilters = generateMockFilters({ timeframe: 'week' });
      const { result, rerender } = renderHook(
        ({ filters }) => useDynamicKpis({ filters }),
        {
          ...renderHookWithProviders(() => useDynamicKpis({ filters: initialFilters })),
          initialProps: { filters: initialFilters }
        }
      );

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.isInitialLoading).toBe(false);
      });

      expect(result.current.kpis).toEqual(initialKpis);

      // Change filters and mock new response
      mockFetch(mockApiResponse(updatedKpis));
      const newFilters = generateMockFilters({ timeframe: 'month' });
      
      rerender({ filters: newFilters });

      // Should show filter changing state
      expect(result.current.isFilterChanging).toBe(true);

      await waitFor(() => {
        expect(result.current.isFilterChanging).toBe(false);
      });

      expect(result.current.kpis).toEqual(updatedKpis);
    });

    it('should build correct query parameters from filters', async () => {
      const mockKpis = [generateMockKpi()];
      const mockFetchSpy = mockFetch(mockApiResponse(mockKpis));

      const filters = generateMockFilters({
        timeframe: 'month',
        currency: 'usd',
        category: 'volume',
        priority_only: false
      });

      renderHook(() => useDynamicKpis({ filters }), renderHookWithProviders(() => useDynamicKpis({ filters })));

      await waitFor(() => {
        expect(mockFetchSpy).toHaveBeenCalled();
      });

      const callUrl = mockFetchSpy.mock.calls[0][0] as string;
      expect(callUrl).toContain('timeframe=month');
      expect(callUrl).toContain('currency=usd');
      expect(callUrl).toContain('category=volume');
      expect(callUrl).toContain('priority_only=false');
    });
  });

  describe('Manual Refresh', () => {
    it('should refresh KPIs when refreshKpis is called', async () => {
      const initialKpis = [generateMockKpi({ id: 'kpi1' })];
      const refreshedKpis = [generateMockKpi({ id: 'kpi1', currentValue: 2000 })];
      
      mockFetch(mockApiResponse(initialKpis));

      const filters = generateMockFilters();
      const { result } = renderHook(() => useDynamicKpis({ filters }), renderHookWithProviders(() => useDynamicKpis({ filters })));

      await waitFor(() => {
        expect(result.current.isInitialLoading).toBe(false);
      });

      // Mock refresh response
      mockFetch(mockApiResponse(refreshedKpis));

      act(() => {
        result.current.refreshKpis();
      });

      expect(result.current.isRefreshing).toBe(true);

      await waitFor(() => {
        expect(result.current.isRefreshing).toBe(false);
      });

      expect(result.current.kpis).toEqual(refreshedKpis);
    });

    it('should add force_refresh parameter when refreshing', async () => {
      const mockKpis = [generateMockKpi()];
      const mockFetchSpy = mockFetch(mockApiResponse(mockKpis));

      const filters = generateMockFilters();
      const { result } = renderHook(() => useDynamicKpis({ filters }), renderHookWithProviders(() => useDynamicKpis({ filters })));

      await waitFor(() => {
        expect(result.current.isInitialLoading).toBe(false);
      });

      // Clear previous calls
      mockFetchSpy.mockClear();

      act(() => {
        result.current.refreshKpis();
      });

      await waitFor(() => {
        expect(mockFetchSpy).toHaveBeenCalled();
      });

      const callUrl = mockFetchSpy.mock.calls[0][0] as string;
      expect(callUrl).toContain('force_refresh=true');
    });
  });

  describe('KPI Actions', () => {
    it('should toggle KPI priority', async () => {
      const mockKpis = [
        generateMockKpi({ id: 'kpi1', is_priority: false }),
        generateMockKpi({ id: 'kpi2', is_priority: true })
      ];
      mockFetch(mockApiResponse(mockKpis));

      const filters = generateMockFilters();
      const { result } = renderHook(() => useDynamicKpis({ filters }), renderHookWithProviders(() => useDynamicKpis({ filters })));

      await waitFor(() => {
        expect(result.current.isInitialLoading).toBe(false);
      });

      act(() => {
        result.current.togglePriority('kpi1');
      });

      const updatedKpi = result.current.kpis.find(kpi => kpi.id === 'kpi1');
      expect(updatedKpi?.is_priority).toBe(true);
    });

    it('should remove KPI from list', async () => {
      const mockKpis = [
        generateMockKpi({ id: 'kpi1' }),
        generateMockKpi({ id: 'kpi2' })
      ];
      mockFetch(mockApiResponse(mockKpis));

      const filters = generateMockFilters();
      const { result } = renderHook(() => useDynamicKpis({ filters }), renderHookWithProviders(() => useDynamicKpis({ filters })));

      await waitFor(() => {
        expect(result.current.isInitialLoading).toBe(false);
      });

      expect(result.current.kpis).toHaveLength(2);

      act(() => {
        result.current.removeKpi('kpi1');
      });

      expect(result.current.kpis).toHaveLength(1);
      expect(result.current.kpis[0].id).toBe('kpi2');
    });

    it('should find KPI by ID', async () => {
      const mockKpis = [generateMockKpi({ id: 'test_kpi', name: 'Test KPI' })];
      mockFetch(mockApiResponse(mockKpis));

      const filters = generateMockFilters();
      const { result } = renderHook(() => useDynamicKpis({ filters }), renderHookWithProviders(() => useDynamicKpis({ filters })));

      await waitFor(() => {
        expect(result.current.isInitialLoading).toBe(false);
      });

      const foundKpi = result.current.getKpiById('test_kpi');
      expect(foundKpi).toBeDefined();
      expect(foundKpi?.name).toBe('Test KPI');

      const notFoundKpi = result.current.getKpiById('nonexistent');
      expect(notFoundKpi).toBeUndefined();
    });
  });

  describe('Auto Refresh', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should auto-refresh when enabled', async () => {
      const mockKpis = [generateMockKpi()];
      const mockFetchSpy = mockFetch(mockApiResponse(mockKpis));

      const filters = generateMockFilters();
      renderHook(() => useDynamicKpis({ 
        filters, 
        autoRefresh: true, 
        refreshInterval: 5000 
      }), renderHookWithProviders(() => useDynamicKpis({ filters })));

      // Wait for initial load
      await waitFor(() => {
        expect(mockFetchSpy).toHaveBeenCalledTimes(1);
      });

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(5000);
      });

      // Should have called fetch again
      await waitFor(() => {
        expect(mockFetchSpy).toHaveBeenCalledTimes(2);
      });
    });

    it('should not auto-refresh when disabled', async () => {
      const mockKpis = [generateMockKpi()];
      const mockFetchSpy = mockFetch(mockApiResponse(mockKpis));

      const filters = generateMockFilters();
      renderHook(() => useDynamicKpis({ 
        filters, 
        autoRefresh: false 
      }), renderHookWithProviders(() => useDynamicKpis({ filters })));

      // Wait for initial load
      await waitFor(() => {
        expect(mockFetchSpy).toHaveBeenCalledTimes(1);
      });

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(30000);
      });

      // Should not have called fetch again
      expect(mockFetchSpy).toHaveBeenCalledTimes(1);
    });
  });
});

describe('kpiHelpers', () => {
  describe('sortKpis', () => {
    it('should sort KPIs by priority and display order', () => {
      const kpis = [
        generateMockKpi({ id: 'kpi1', is_priority: false, display_order: 2 }),
        generateMockKpi({ id: 'kpi2', is_priority: true, display_order: 3 }),
        generateMockKpi({ id: 'kpi3', is_priority: true, display_order: 1 }),
        generateMockKpi({ id: 'kpi4', is_priority: false, display_order: 1 }),
      ];

      const sorted = kpiHelpers.sortKpis(kpis);

      expect(sorted[0].id).toBe('kpi3'); // Priority + order 1
      expect(sorted[1].id).toBe('kpi2'); // Priority + order 3
      expect(sorted[2].id).toBe('kpi4'); // Non-priority + order 1
      expect(sorted[3].id).toBe('kpi1'); // Non-priority + order 2
    });
  });

  describe('filterByCategory', () => {
    it('should filter KPIs by category', () => {
      const kpis = [
        generateMockKpi({ id: 'kpi1', category: 'volume' }),
        generateMockKpi({ id: 'kpi2', category: 'performance' }),
        generateMockKpi({ id: 'kpi3', category: 'volume' }),
      ];

      const volumeKpis = kpiHelpers.filterByCategory(kpis, 'volume');
      expect(volumeKpis).toHaveLength(2);
      expect(volumeKpis.every(kpi => kpi.category === 'volume')).toBe(true);
    });
  });

  describe('getCategories', () => {
    it('should return unique categories sorted', () => {
      const kpis = [
        generateMockKpi({ category: 'volume' }),
        generateMockKpi({ category: 'performance' }),
        generateMockKpi({ category: 'volume' }),
        generateMockKpi({ category: 'compliance' }),
      ];

      const categories = kpiHelpers.getCategories(kpis);
      expect(categories).toEqual(['compliance', 'performance', 'volume']);
    });
  });

  describe('getPriorityKpis', () => {
    it('should return only priority KPIs', () => {
      const kpis = [
        generateMockKpi({ id: 'kpi1', is_priority: true }),
        generateMockKpi({ id: 'kpi2', is_priority: false }),
        generateMockKpi({ id: 'kpi3', is_priority: true }),
      ];

      const priorityKpis = kpiHelpers.getPriorityKpis(kpis);
      expect(priorityKpis).toHaveLength(2);
      expect(priorityKpis.every(kpi => kpi.is_priority)).toBe(true);
    });
  });
});
