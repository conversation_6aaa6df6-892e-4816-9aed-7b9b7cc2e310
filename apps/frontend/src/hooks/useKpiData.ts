import { useState, useEffect } from 'react';
import {
  getDashboardSnapshot,
  convertSnapshotToKpiData,
  type KpiData,
  type KpiAlert
} from '@/lib/api';
import { type DashboardFilters } from './useDashboardFilters';

// Re-export types for backward compatibility
export type { K<PERSON><PERSON><PERSON>, KpiAlert };

export const useKpiData = (filters: DashboardFilters) => {
  console.log('useKpiData hook called with filters:', filters);

  const [kpis, setKpis] = useState<KpiData[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isFilterChanging, setIsFilterChanging] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [snapshotMetadata, setSnapshotMetadata] = useState<any>(null);

  // Backward compatibility
  const isLoading = isInitialLoading || isFilterChanging || isRefreshing;

  useEffect(() => {
    console.log('🔄 useKpiData - Filter change detected:', filters);

    // Set filter changing state for existing data
    if (kpis.length > 0) {
      setIsFilterChanging(true);
    }

    // Debounce filter changes to avoid excessive API calls
    const debounceTimer = setTimeout(() => {
      loadKpisDebounced();
    }, 300); // 300ms debounce

    return () => clearTimeout(debounceTimer);
  }, [filters]);

  // Load KPIs with filter-aware API calls
  const loadKpisDebounced = async () => {
    console.log('⚡ useKpiData - Loading KPIs with filters:', filters);
    setError(null);

    // Don't change initial loading state if we're just changing filters
    if (!isFilterChanging) {
      setIsInitialLoading(true);
    }

    try {
      // Use snapshot API that works with Redis cache
      console.log(`🔍 useKpiData - Calling snapshot API for business data`);
      const snapshot = await getDashboardSnapshot();
      console.log('✅ useKpiData - Snapshot response received:', snapshot);

      if (!snapshot?.data) {
        throw new Error('No snapshot data received');
      }

      // Convert snapshot data to KPI format
      const allKpis = convertSnapshotToKpiData(snapshot);
      setSnapshotMetadata(snapshot.metadata);

      // Define the 6 critical KPIs
      const criticalKpiIds = [
        'total_volume',
        'average_ticket',
        'average_spread',
        'conversion_rate',
        'retention_rate',
        'gross_margin'
      ];

      // Filter for critical KPIs only
      const criticalKpis = allKpis
        .filter((kpi: KpiData) => criticalKpiIds.includes(kpi.id))
        .sort((a: KpiData, b: KpiData) => {
          // Sort by the order in criticalKpiIds array
          const indexA = criticalKpiIds.indexOf(a.id);
          const indexB = criticalKpiIds.indexOf(b.id);
          return indexA - indexB;
        });

      console.log(`📊 useKpiData - Setting ${criticalKpis.length} filtered KPIs:`, criticalKpis.map(k => k.id));

      // Verificar se valores estão sincronizados com filtros
      criticalKpis.forEach(kpi => {
        console.log(`🔍 KPI ${kpi.id} values:`, {
          currentValue: kpi.currentValue,
          changePercent: kpi.changePercent,
          trend: kpi.trend,
          chartDataLength: kpi.chartData?.length || 0
        });
      });

      setKpis(criticalKpis);

    } catch (err) {
      console.error('❌ useKpiData - API failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to load KPIs');
      setKpis([]);
    } finally {
      setIsInitialLoading(false);
      setIsFilterChanging(false);
      setIsRefreshing(false);
      console.log('🏁 useKpiData - Loading completed');
    }
  };

  const togglePriority = (kpiId: string) => {
    setKpis(prevKpis =>
      prevKpis.map(kpi =>
        kpi.id === kpiId
          ? { ...kpi, isPriority: !kpi.isPriority }
          : kpi
      )
    );
  };

  const refreshKpis = async () => {
    console.log('🔄 useKpiData - Manual refresh triggered');
    setIsRefreshing(true);
    setError(null);

    try {
      // Use snapshot API that works with Redis cache
      const snapshot = await getDashboardSnapshot();

      if (!snapshot?.data) {
        throw new Error('No snapshot data received during refresh');
      }

      // Convert snapshot data to KPI format
      const allKpis = convertSnapshotToKpiData(snapshot);
      setSnapshotMetadata(snapshot.metadata);

      // Define the 6 critical KPIs
      const criticalKpiIds = [
        'total_volume',
        'average_ticket',
        'average_spread',
        'conversion_rate',
        'retention_rate',
        'gross_margin'
      ];

      // Filter for critical KPIs only
      const criticalKpis = allKpis
        .filter((kpi: KpiData) => criticalKpiIds.includes(kpi.id))
        .sort((a: KpiData, b: KpiData) => {
          // Sort by the order in criticalKpiIds array
          const indexA = criticalKpiIds.indexOf(a.id);
          const indexB = criticalKpiIds.indexOf(b.id);
          return indexA - indexB;
        });

      setKpis(criticalKpis);
    } catch (err) {
      console.error('❌ useKpiData - Refresh error:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh KPIs');
    } finally {
      setIsRefreshing(false);
      console.log('🏁 useKpiData - Refresh completed');
    }
  };

  return {
    kpis,
    isLoading, // Backward compatibility
    isInitialLoading,
    isFilterChanging,
    isRefreshing,
    error,
    togglePriority,
    refreshKpis,
    snapshotMetadata
  };
};
