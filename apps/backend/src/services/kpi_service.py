"""
Modern KPI Service with SQLModel Repository
===========================================

Clean, modern KPI service using:
- SQLModel ORM for type-safe queries
- Repository pattern for data access
- Unified cache system
- Clean separation of concerns
"""

import logging
import os
import time
import threading
from typing import Dict, List, Any, Optional
from sqlmodel import Session, create_engine
from contextlib import contextmanager
from concurrent.futures import ThreadPoolExecutor, as_completed

from src.caching.unified_cache_system import get_unified_cache
from src.repositories.kpi_repository import KpiRepository
from src.config.kpi_definitions import get_kpi_definition, CRITICAL_KPIS
from src.services.kpi_query_manager_json import KpiQueryManagerJSON

logger = logging.getLogger(__name__)


class KpiService:
    """Modern KPI service with repository pattern."""
    
    def __init__(self):
        self.cache = get_unified_cache()
        self.engine = self._create_engine()
        
        # Query manager for JSON-based queries
        self.query_manager = KpiQueryManagerJSON(sector="cambio")
        
        # Thread pool for parallel KPI calculation
        self.executor = ThreadPoolExecutor(max_workers=6)
        self._cache_lock = threading.Lock()
        
        logger.info("✅ Modern KPI Service initialized with parallel processing")

    def _create_engine(self):
        """Create SQLAlchemy engine from environment variables."""
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            raise ValueError("DATABASE_URL environment variable is required and not set")

        # Configurações robustas para AWS RDS
        return create_engine(
            database_url, 
            echo=False,
            pool_size=10,          # Tamanho do pool de conexões
            max_overflow=20,       # Conexões extras se necessário
            pool_timeout=30,       # Timeout para obter conexão do pool
            pool_recycle=3600,     # Reciclar conexões a cada hora
            pool_pre_ping=True,    # Verificar conexão antes de usar
            connect_args={
                "connect_timeout": 30,    # Timeout de conexão: 30s
                "application_name": "datahero4_kpi_service"
            }
        )
    
    @contextmanager
    def get_repository(self):
        """Get KPI repository with database session."""
        try:
            with Session(self.engine) as session:
                yield KpiRepository(session)
        except Exception as e:
            logger.error(f"Error creating repository: {e}")
            raise
    
    def get_dashboard_kpis(
        self,
        sector: str = "cambio",
        client_id: str = "L2M",
        timeframe: str = "week",
        currency: str = "all",
        priority_only: bool = True,
        category: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get dashboard KPIs with caching and parallel processing.

        Args:
            sector: Business sector (cambio)
            client_id: Client identifier
            timeframe: Time period for calculations
            currency: Currency filter
            priority_only: Return only priority KPIs
            category: Optional category filter

        Returns:
            List of calculated KPI data
        """
        try:
            logger.info(f"📊 Getting dashboard KPIs - timeframe: {timeframe}, currency: {currency}, priority_only: {priority_only}")
            
            # Check cache first
            cached_result = self.cache.get(
                "kpi:dashboard",
                client_id=client_id,
                timeframe=timeframe,
                category=category,
                priority_only=priority_only,
                currency=currency
            )
            
            if cached_result is not None:
                logger.info(f"🎯 Dashboard KPIs from cache: {len(cached_result)} KPIs (priority_only={priority_only})")
                return cached_result

            logger.info(f"🔍 Cache MISS - calculating fresh KPIs (priority_only={priority_only})")

            # Load KPI definitions
            kpi_definitions = self._load_kpi_definitions(
                sector=sector,
                category=category,
                priority_only=priority_only
            )
            
            # Calculate KPIs in parallel for better performance
            kpis = self._calculate_kpis_parallel(
                kpi_definitions, client_id, timeframe, currency
            )
            
            # Cache result
            self.cache.set(
                "kpi:dashboard",
                kpis,
                timeframe=timeframe,
                client_id=client_id,
                category=category,
                priority_only=priority_only,
                currency=currency
            )
            
            logger.info(f"✅ Calculated {len(kpis)} KPIs with parallel processing")
            return kpis
                
        except Exception as e:
            logger.error(f"Error getting dashboard KPIs: {e}")
            raise
    
    def calculate_single_kpi(
        self,
        kpi_id: str,
        sector: str = "cambio",
        timeframe: str = "week",
        currency: str = "all"
    ) -> Optional[Dict[str, Any]]:
        """
        Calculate a single KPI on demand.

        Args:
            kpi_id: KPI identifier
            sector: Business sector (cambio)
            timeframe: Time period
            currency: Currency filter

        Returns:
            Single KPI data or None if not found
        """
        try:
            # Check cache first
            cache_key = f"single_kpi:{kpi_id}:{sector}:{timeframe}:{currency}"
            cached_result = self.cache.get(cache_key)

            if cached_result is not None:
                logger.info(f"✅ Single KPI {kpi_id} from cache")
                return cached_result

            # Calculate using repository
            with self.get_repository() as repo:
                # Calculate KPI directly - no client validation needed

                kpi_data = repo.calculate_single_kpi(
                    kpi_id=kpi_id,
                    timeframe=timeframe,
                    currency=currency
                )
                
                if kpi_data:
                    # Cache with KPI-specific TTL
                    kpi_def = get_kpi_definition(kpi_id)
                    cache_ttl = kpi_def.get('cache_ttl', 300)
                    self.cache.set(cache_key, kpi_data, ttl=cache_ttl)
                    
                    logger.info(f"✅ Calculated single KPI {kpi_id}")
                
                return kpi_data
                
        except Exception as e:
            logger.error(f"Error calculating single KPI {kpi_id}: {e}")
            raise
    
    def invalidate_kpis(
        self,
        kpi_ids: Optional[List[str]] = None,
        client_id: Optional[str] = None,
        timeframe: Optional[str] = None,
        currency: Optional[str] = None
    ):
        """
        Invalidate KPI cache selectively.
        
        Args:
            kpi_ids: List of KPI IDs to invalidate
            client_id: Client to invalidate
            timeframe: Timeframe to invalidate
            currency: Currency to invalidate
        """
        try:
            if kpi_ids is None:
                kpi_ids = CRITICAL_KPIS
            
            patterns_to_invalidate = []
            
            # Dashboard KPIs patterns
            if client_id and timeframe and currency:
                patterns_to_invalidate.append(
                    f"dashboard_kpis:{client_id}:*:{timeframe}:{currency}:*"
                )
            
            # Single KPI patterns
            for kpi_id in kpi_ids:
                if client_id:
                    patterns_to_invalidate.append(
                        f"single_kpi:{kpi_id}:{client_id}:*"
                    )
                else:
                    patterns_to_invalidate.append(
                        f"single_kpi:{kpi_id}:*"
                    )
            
            # Invalidate cache patterns
            for pattern in patterns_to_invalidate:
                self.cache.invalidate_pattern(pattern)
            
            logger.info(f"🔄 Invalidated cache for {len(kpi_ids)} KPIs")
            
        except Exception as e:
            logger.error(f"Error invalidating KPI cache: {e}")
    
    def get_kpi_metadata(self, kpi_id: str) -> Optional[Dict[str, Any]]:
        """Get KPI metadata from configuration."""
        return get_kpi_definition(kpi_id)
    
    def get_available_kpis(self, priority_only: bool = False) -> List[Dict[str, Any]]:
        """Get list of available KPIs."""
        if priority_only:
            return [get_kpi_definition(kpi_id) for kpi_id in CRITICAL_KPIS]
        
        from src.config.kpi_definitions import KPI_DEFINITIONS
        return list(KPI_DEFINITIONS.values())
    
    def _calculate_kpis_parallel(
        self,
        kpi_definitions: List[Dict[str, Any]],
        client_id: str,
        timeframe: str,
        currency: str
    ) -> List[Dict[str, Any]]:
        """
        Calculate multiple KPIs in parallel using ThreadPoolExecutor.
        """
        start_time = time.time()
        results = []
        future_to_kpi = {}

        # Submit all tasks
        for kpi_def in kpi_definitions:
            future = self.executor.submit(
                self._calculate_single_kpi_thread_safe,
                kpi_def, client_id, timeframe, currency
            )
            future_to_kpi[future] = kpi_def

        # Collect results as they complete
        for future in as_completed(future_to_kpi):
            kpi_def = future_to_kpi[future]
            try:
                result = future.result(timeout=10)  # 10s timeout per KPI
                if result:
                    results.append(result)
                else:
                    logger.warning(f"⚠️ KPI {kpi_def.get('id')} returned None")
            except Exception as e:
                logger.error(f"❌ Error calculating KPI {kpi_def.get('id')}: {e}")
                # Continue with other KPIs instead of failing fast
                continue

        parallel_time = (time.time() - start_time) * 1000
        logger.info(f"🚀 Calculated {len(results)} KPIs in parallel in {parallel_time:.0f}ms")

        return results



    def _calculate_single_kpi_thread_safe(
        self,
        kpi_def: Dict[str, Any],
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[Dict[str, Any]]:
        """
        Thread-safe version of single KPI calculation.
        """
        kpi_id = kpi_def.get('id', 'unknown')

        # Check cache with lock
        with self._cache_lock:
            cached_result = self.cache.get(
                "kpi:full",
                kpi_id=kpi_id,
                client_id=client_id,
                timeframe=timeframe,
                currency=currency
            )
            if cached_result:
                logger.info(f"🎯 Cache hit for {kpi_id}")
                return cached_result

        # Calculate if not cached
        start_time = time.time()

        try:
            result = self._calculate_single_kpi_data(kpi_def, client_id, timeframe, currency)

            if result:
                # Cache result
                with self._cache_lock:
                    self.cache.set(
                        "kpi:full",
                        result,
                        kpi_id=kpi_id,
                        client_id=client_id,
                        timeframe=timeframe,
                        currency=currency,
                        ttl=1800  # 30 minutes
                    )

                calc_time = round((time.time() - start_time) * 1000, 2)
                logger.info(f"✅ Calculated {kpi_id} in {calc_time}ms")

            return result

        except Exception as e:
            logger.error(f"❌ Error in thread-safe calculation for {kpi_id}: {e}")
            return None

    def _map_unit_to_format_type(self, unit: str) -> str:
        """Map unit string to format type for frontend compatibility."""
        if not unit:
            return 'number'

        unit_lower = unit.lower()
        if 'r$' in unit_lower or 'us$' in unit_lower or 'monetário' in unit_lower or 'valor monetário' in unit_lower:
            return 'currency'
        elif '%' in unit_lower or 'percentual' in unit_lower:
            return 'percentage'
        else:
            return 'number'

    def _calculate_single_kpi_data(
        self,
        kpi_def: Dict[str, Any],
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[Dict[str, Any]]:
        """Calculate single KPI data."""
        kpi_id = kpi_def.get('id')
        
        # Calculate value using query manager
        value = self._calculate_kpi_value(
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        if value is None:
            return None
        
        # Generate chart data
        chart_data = self._generate_chart_data(
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )

        # Map unit to format type
        format_type = self._map_unit_to_format_type(kpi_def.get('unit', ''))

        # Format response
        return {
            'id': kpi_id,
            'title': kpi_def.get('name', ''),
            'description': kpi_def.get('description', ''),
            'currentValue': value,
            'format': format_type,
            'changePercent': self._calculate_change_percent(kpi_id, value, timeframe),
            'trend': self._determine_trend(kpi_id, value),
            'chartType': kpi_def.get('chart_type', 'line'),
            'chartData': chart_data,
            'alert': self._check_alert(kpi_id, value),
            'isPriority': kpi_def.get('is_priority', False),
            'order': kpi_def.get('display_order', 999),
            'category': kpi_def.get('category', 'general'),
            'unit': kpi_def.get('unit', ''),
            'frequency': kpi_def.get('frequency', 'daily')
        }

    def _load_kpi_definitions(
        self,
        sector: str,
        category: Optional[str],
        priority_only: bool
    ) -> List[Dict[str, Any]]:
        """Load KPI definitions from JSON configuration."""
        try:
            # Use the query manager to get definitions
            all_kpis = self.query_manager.get_all_kpi_definitions()

            # List of priority KPIs
            priority_kpis = [
                'total_volume',
                'average_ticket',
                'average_spread',
                'conversion_rate',
                'retention_rate',
                'gross_margin'
            ]

            # Filter KPIs
            filtered_kpis = []
            for kpi in all_kpis:
                kpi_id = kpi.get('id')

                # Apply priority filter
                if priority_only and kpi_id not in priority_kpis:
                    continue

                # Apply category filter
                if category and kpi.get('category') != category:
                    continue

                # Mark as priority
                kpi['is_priority'] = kpi_id in priority_kpis

                # Set display order
                if kpi_id in priority_kpis:
                    kpi['display_order'] = priority_kpis.index(kpi_id) + 1
                else:
                    kpi['display_order'] = 999

                filtered_kpis.append(kpi)

            # Sort by priority
            filtered_kpis.sort(key=lambda x: x.get('display_order', 999))

            return filtered_kpis

        except Exception as e:
            logger.error(f"❌ Error loading KPI definitions: {e}")
            raise ValueError(f"Failed to load KPI definitions: {e}")

    def _calculate_kpi_value(
        self,
        kpi_id: str,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[float]:
        """Calculate KPI value using query manager."""
        # Check cache
        cached_value = self.cache.get(
            "kpi:value",
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        if cached_value is not None:
            return cached_value
        
        # Get dynamic query
        dynamic_query = self.query_manager.get_kpi_query(kpi_id)
        if not dynamic_query:
            logger.error(f"No query definition found for KPI: {kpi_id}")
            return None

        value = self._execute_query(dynamic_query, timeframe, currency)

        if value is None:
            logger.error(f"Failed to calculate KPI {kpi_id} with filters: timeframe={timeframe}, currency={currency}")
            return None
        
        # Cache result
        if value is not None:
            self.cache.set(
                "kpi:value",
                value,
                timeframe=timeframe,
                kpi_id=kpi_id,
                client_id=client_id,
                currency=currency
            )
        
        return value

    def _generate_chart_data(
        self,
        kpi_id: str,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> List[Dict[str, Any]]:
        """Generate chart data for KPI."""
        # Check cache
        cached_data = self.cache.get(
            "kpi:chart",
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )

        if cached_data is not None:
            return cached_data

        # Generate real chart data
        chart_data = self._get_real_chart_data(kpi_id, client_id, timeframe, currency)

        if not chart_data:
            logger.error(f"No chart data available for KPI {kpi_id}")
            return []

        # Cache result
        self.cache.set(
            "kpi:chart",
            chart_data,
            timeframe=timeframe,
            kpi_id=kpi_id,
            client_id=client_id,
            currency=currency
        )

        return chart_data

    def _get_real_chart_data(
        self,
        kpi_id: str,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> List[Dict[str, Any]]:
        """Get real chart data from database using KPI-specific queries."""
        try:
            from sqlalchemy import text

            # Use the service's engine
            engine = self.engine

            # Determine period for historical data
            if timeframe == "week":
                days_back = 7
            elif timeframe == "month":
                days_back = 30
            else:
                days_back = 7

            # SIMULATION: Use a fixed date range since the database is static (only has data until Jan 2025)
            simulated_current_date = "2025-01-15"

            # Get the specific KPI query for this KPI
            kpi_query = self.query_manager.get_kpi_query(kpi_id)
            if not kpi_query:
                logger.error(f"No query found for KPI {kpi_id} - cannot generate chart data")
                return []

            # Build KPI-specific historical query by adapting the main query
            historical_query = self._build_kpi_historical_query(
                kpi_id, kpi_query, simulated_current_date, days_back, currency
            )

            # If query adaptation failed (no fallbacks allowed), return empty data
            if historical_query is None:
                logger.info(f"No chart data for {kpi_id} - query adaptation not possible without fallbacks")
                return []

            chart_data = []
            with engine.connect() as conn:
                result = conn.execute(text(historical_query))
                rows = result.fetchall()

                for row in rows:
                    date_obj = row[0]
                    value = float(row[1]) if row[1] is not None else 0.0

                    chart_data.append({
                        "name": date_obj.strftime("%d/%m"),
                        "value": round(value, 2),
                        "date": date_obj.strftime("%Y-%m-%d")
                    })

            return chart_data

        except Exception as e:
            logger.warning(f"⚠️ Could not generate chart data for {kpi_id}: {e}")
            return []

    def _build_kpi_historical_query(
        self,
        kpi_id: str,
        kpi_query: str,
        simulated_current_date: str,
        days_back: int,
        currency: str
    ) -> str:
        """Build historical query by adapting the existing KPI query for daily grouping."""

        # Apply date range filter
        timeframe_sql = f"data_operacao >= DATE('{simulated_current_date}') - INTERVAL '{days_back} days' AND data_operacao <= DATE('{simulated_current_date}')"
        currency_sql = self._get_currency_sql(currency)

        # Replace placeholders in the original query
        adapted_query = kpi_query
        if ':timeframe_filter' in adapted_query:
            adapted_query = adapted_query.replace(':timeframe_filter', timeframe_sql)
        if ':currency_filter' in adapted_query:
            adapted_query = adapted_query.replace(':currency_filter', currency_sql)

        # For complex queries (like retention_rate with WITH clauses), return None
        # This will skip chart generation rather than using fallbacks
        if "WITH" in adapted_query.upper():
            logger.info(f"Skipping chart generation for complex query {kpi_id} - no fallbacks allowed")
            return None

        # Extract the calculation part from the existing query
        if "SELECT" in adapted_query.upper() and "FROM" in adapted_query.upper():
            # Find the calculation part (between SELECT and FROM)
            select_part = adapted_query.split("FROM")[0].replace("SELECT", "").strip()
            from_and_where_part = "FROM " + adapted_query.split("FROM", 1)[1]

            # Replace the alias with a generic one and adapt for daily calculation
            calculation = select_part.split(" as ")[0].strip() if " as " in select_part else select_part.strip()

            # Build the historical query with daily grouping
            historical_query = f"""
            SELECT
                DATE(data_operacao) as date,
                {calculation} as daily_value
            {from_and_where_part}
            GROUP BY DATE(data_operacao)
            ORDER BY date
            """

            return historical_query

        # No fallbacks - return None if we can't adapt the query properly
        logger.info(f"Cannot adapt query for {kpi_id} without fallbacks - skipping chart generation")
        return None




    def _execute_query(self, query: str, timeframe: str, currency: str) -> Optional[float]:
        """Execute SQL query with filters using the service's engine."""
        try:
            from src.tools.db_utils import load_db_config, build_connection_string
            from sqlalchemy import text

            # Use the service's engine instead of creating a new one
            # This reuses the connection pool and avoids connection exhaustion
            engine = self.engine
            
            # Apply filters
            timeframe_sql = self._get_timeframe_sql(timeframe)
            currency_sql = self._get_currency_sql(currency)
            
            # Replace placeholders
            query_with_filters = query
            if ':timeframe_filter' in query:
                query_with_filters = query_with_filters.replace(':timeframe_filter', timeframe_sql)
            if ':currency_filter' in query:
                query_with_filters = query_with_filters.replace(':currency_filter', currency_sql)
            
            # Execute query
            with engine.connect() as conn:
                result = conn.execute(text(query_with_filters))
                row = result.fetchone()
                
                if row and row[0] is not None:
                    return float(row[0])
                    
            return None
            
        except Exception as e:
            logger.error(f"❌ Error executing query: {e}")
            return None

    def _get_timeframe_sql(self, timeframe: str) -> str:
        """Convert timeframe to SQL filter."""
        mapping = {
            '1d': "data_operacao >= (SELECT MAX(data_operacao) FROM boleta)",
            'week': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '7 days' FROM boleta)",
            'month': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '30 days' FROM boleta)",
            'quarter': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '90 days' FROM boleta)"
        }
        return mapping.get(timeframe, mapping['week'])

    def _get_currency_sql(self, currency: str) -> str:
        """Convert currency to SQL filter."""
        if currency == 'all':
            return "1=1"

        # Map currency codes to IDs
        currency_mapping = {
            'USD': 4,   # US Dollar
            'EUR': 14,  # Euro
            'BRL': 1,   # Brazilian Real
            'GBP': 3    # British Pound
        }

        currency_id = currency_mapping.get(currency.upper())
        if currency_id:
            return f"id_moeda = {currency_id}"
        else:
            return f"""id_moeda IN (
                SELECT id FROM boleta_moeda
                WHERE UPPER(simbolo) = '{currency.upper()}'
            )"""

    def _calculate_change_percent(self, kpi_id: str, current_value: float, timeframe: str) -> float:
        """Calculate percentage change using real data."""
        try:
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            from sqlalchemy import text

            # Connect to database
            db_config = load_db_config(setor="cambio", cliente="L2M")
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)

            # Determine previous period
            if timeframe == "week":
                previous_period = "CURRENT_DATE - INTERVAL '14 days' AND CURRENT_DATE - INTERVAL '7 days'"
            elif timeframe == "month":
                previous_period = "CURRENT_DATE - INTERVAL '60 days' AND CURRENT_DATE - INTERVAL '30 days'"
            else:
                previous_period = "CURRENT_DATE - INTERVAL '14 days' AND CURRENT_DATE - INTERVAL '7 days'"

            # Query for previous period value
            if kpi_id == "total_volume":
                query = f"""
                SELECT COALESCE(SUM(valor_me), 0) as previous_value
                FROM boleta
                WHERE data_operacao BETWEEN {previous_period}
                AND valor_me IS NOT NULL
                AND valor_me > 0
                """
            elif kpi_id == "average_ticket":
                query = f"""
                SELECT COALESCE(AVG(valor_me), 0) as previous_value
                FROM boleta
                WHERE data_operacao BETWEEN {previous_period}
                AND valor_me IS NOT NULL
                AND valor_me > 0
                """
            else:
                return 0.0

            with engine.connect() as conn:
                result = conn.execute(text(query))
                row = result.fetchone()
                previous_value = float(row[0]) if row and row[0] else 0

            # Calculate percentage change
            if previous_value == 0:
                return 0.0

            change_percent = ((current_value - previous_value) / previous_value) * 100
            return round(change_percent, 2)

        except Exception as e:
            logger.error(f"❌ Error calculating change percent for {kpi_id}: {e}")
            return 0.0

    def _determine_trend(self, kpi_id: str, value: float) -> str:
        """Determine KPI trend."""
        # TODO: Implement real trend analysis
        import random
        return random.choice(['up', 'down', 'stable'])

    def _check_alert(self, kpi_id: str, value: float) -> Optional[Dict[str, Any]]:
        """Check alerts for KPI."""
        # TODO: Implement real alert checking
        return None

    def _get_cache_ttl(self, timeframe: str) -> int:
        """Get appropriate cache TTL based on timeframe."""
        ttl_map = {
            "1d": 300,      # 5 minutes for daily data
            "week": 600,    # 10 minutes for weekly data
            "month": 1800,  # 30 minutes for monthly data
            "quarter": 3600 # 1 hour for quarterly data
        }
        return ttl_map.get(timeframe, 600)


# Singleton instance
_kpi_service_instance = None


def get_kpi_service() -> KpiService:
    """Get singleton KPI service instance."""
    global _kpi_service_instance
    if _kpi_service_instance is None:
        _kpi_service_instance = KpiService()
    return _kpi_service_instance


